[versions]
compileSdkVersion = "35"
targetSdkVersion = "35"
minSdkVersion = "24"
javaVersion = "17"

agp = "8.10.1"

composeSwipeboxMultiplatform = "1.2.0"
photoview = "2.0.0"
firebaseInstallations = "18.0.0"
lottie = "6.6.6"
coil = "2.6.0"
kotlinxIoCore = "0.5.4"
securityCrypto = "1.1.0-alpha07"
appUpdateVersion = "2.1.0"
constraintLayoutVersion = "2.2.1"
glideCompose = "1.0.0-beta01"
desugarJdkLibs = "2.1.5"
firebaseCrashlyticsGradle = "3.0.3"
glide = "4.16.0"
googleServices = "4.4.2"
googleServicesAuth = "21.3.0"
googleIdVersion = "1.1.1"
gson = "2.11.0"
guava = "33.4.0-android"
jodaTime = "2.12.5"
kotlin = "2.0.21"
coreKtx = "1.16.0"
appcompat = "1.7.0"
kotlinxCoroutinesCore = "1.6.1"
leakcanaryAndroidVersion = "2.14"
material = "1.12.0"
androidxComposeBom = "2025.02.00"
composeCompiler = "1.5.15"
androidxLifecycle = "2.9.0"
multidex = "2.0.1"
navigationComposeVersion = "2.9.0"
perfPlugin = "1.4.2"
appdistribution = "5.1.1"
permissionx = "1.7.1"
roomdb = "2.7.1"
shimmerVersion = "0.5.0"
workRuntime = "2.10.1"
ksp = "2.0.21-1.0.26"
balloon = "1.6.5"
cameraVersion = "1.4.2"
firebaseBomVersion = "33.13.0"
androidxNavigationSafeargs = "2.9.0"
okhttp = "5.0.0-alpha.11"
umpVersion = "3.2.0"
archLifecycle = "1.1.1"
adsVersion = "23.6.0"
constraintLayout = "2.1.4"
commonmark = "0.24.0"
accompanistSystemuicontroller = "0.27.0"
accompanistPermissions = "0.35.2-beta"
diHilt = "2.55"
diHiltNaigationCompose = "1.2.0"
haliliboRichtext = '0.16.0'
kotlinxSerializationJson = "1.7.3"
jtokkit = "1.1.0"
billingVersion = "7.1.1"
ktor = "2.3.2"
windowVersion = "1.3.0"
firebaseStorageKtx = "21.0.1"
media3 = "1.7.1"

[libraries]
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanistSystemuicontroller" }
androidx-media3-common = { module = "androidx.media3:media3-common", version.ref = "media3" }
androidx-media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3" }
androidx-media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3" }
androidx-security-crypto = { module = "androidx.security:security-crypto", version.ref = "securityCrypto" }
compose-swipebox-multiplatform = { module = "io.github.kevinnzou:compose-swipebox-multiplatform", version.ref = "composeSwipeboxMultiplatform" }
firebase-appcheck-debug = { module = "com.google.firebase:firebase-appcheck-debug" }
firebase-installations = { module = "com.google.firebase:firebase-installations", version.ref = "firebaseInstallations" }
firebase-vertexai = { module = "com.google.firebase:firebase-vertexai" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "kotlinxCoroutinesCore" }
kotlinx-io-core = { module = "org.jetbrains.kotlinx:kotlinx-io-core", version.ref = "kotlinxIoCore" }
leakcanary-android = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "leakcanaryAndroidVersion" }
lottie = { module = "com.airbnb.android:lottie", version.ref = "lottie" }
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanistPermissions" }
androidx-camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraVersion" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "cameraVersion" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraVersion" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraVersion" }
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-multidex = { module = "androidx.multidex:multidex", version.ref = "multidex" }
androidx-work-runtime = { module = "androidx.work:work-runtime", version.ref = "workRuntime" }
androidx-work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "workRuntime" }
coil = { module = "io.coil-kt:coil", version.ref = "coil" }
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coil" }
glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "glide" }
glide-compose = { module = "com.github.bumptech.glide:compose", version.ref = "glideCompose" }
desugarJdkLibs = { module = "com.android.tools:desugar_jdk_libs", version.ref = "desugarJdkLibs" }
glide = { module = "com.github.bumptech.glide:glide", version.ref = "glide" }
gson = { module = "com.google.code.gson:gson", version.ref = "gson" }
guava = { module = "com.google.guava:guava", version.ref = "guava" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-lifecycle-runtime = { group = "androidx.lifecycle", name = "lifecycle-runtime", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-service = { group = "androidx.lifecycle", name = "lifecycle-service", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-savedstate = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-savedstate", version.ref = "androidxLifecycle" }
androidx-lifecycle-runtime-compose-android = { group = "androidx.lifecycle", name = "lifecycle-runtime-compose-android", version.ref = "androidxLifecycle" }
halilibo-compose-richtext-ui-material3 = { group = "com.halilibo.compose-richtext", name = "richtext-ui-material3", version.ref = "haliliboRichtext" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
jtokkit = { module = "com.knuddels:jtokkit", version.ref = "jtokkit" }

# Compose
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "androidxComposeBom" }
androidx-compose-material3 = { group = "androidx.compose.material3", name = "material3" }
androidx-compose-material = { group = "androidx.compose.material", name = "material" }
androidx-compose-foundation = { group = "androidx.compose.foundation", name = "foundation" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-compose-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-compose-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-compose-material-icons-extended = { group = "androidx.compose.material", name = "material-icons-extended" }
androidx-compose-material3-window-size = { group = "androidx.compose.material3", name = "material3-window-size-class" }
androidx-compose-activity = { group = "androidx.activity", name = "activity-compose" }
androidx-compose-lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose" }
androidx-compose-runtime-livedata = { group = "androidx.compose.runtime", name = "runtime-livedata" }
androidx-compose-navigation = { module = "androidx.navigation:navigation-compose", version.ref = "navigationComposeVersion" }
lifecycle-common-java8 = { module = "android.arch.lifecycle:common-java8", version.ref = "archLifecycle" }
lifecycle-extensions = { module = "android.arch.lifecycle:extensions", version.ref = "archLifecycle" }
photoview = { module = "com.github.chrisbanes:PhotoView", version.ref = "photoview" }
play-services-ads = { module = "com.google.android.gms:play-services-ads", version.ref = "adsVersion" }
androidx-material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintLayoutVersion" }
app-update = { module = "com.google.android.play:app-update", version.ref = "appUpdateVersion" }
app-update-ktx = { module = "com.google.android.play:app-update-ktx", version.ref = "appUpdateVersion" }
billing-ktx = { module = "com.android.billingclient:billing-ktx", version.ref = "billingVersion" }
# Ktor
ktor-client-core = { group = "io.ktor", name = "ktor-client-core", version.ref = "ktor" }
ktor-client-logging = { group = "io.ktor", name = "ktor-client-logging", version.ref = "ktor" }
ktor-client-curl = { group = "io.ktor", name = "ktor-client-curl", version.ref = "ktor" }
ktor-client-auth = { group = "io.ktor", name = "ktor-client-auth", version.ref = "ktor" }
ktor-client-content-negotiation = { group = "io.ktor", name = "ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-serialization-json = { group = "io.ktor", name = "ktor-serialization-kotlinx-json", version.ref = "ktor" }
# Ktor engines
ktor-client-okhttp = { group = "io.ktor", name = "ktor-client-okhttp", version.ref = "ktor" }
# Room Database
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomdb" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomdb" }
androidx-room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomdb" }

# Utils
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
permissionx = { module = "com.guolindev.permissionx:permissionx", version.ref = "permissionx" }
shimmer = { module = "com.facebook.shimmer:shimmer", version.ref = "shimmerVersion" }
user-messaging-platform = { module = "com.google.android.ump:user-messaging-platform", version.ref = "umpVersion" }
joda-time = { module = "joda-time:joda-time", version.ref = "jodaTime" }
balloon-compose = { module = "com.github.skydoves:balloon-compose", version.ref = "balloon" }

# Commonmark
commonmark = { group = "org.commonmark", name = "commonmark", version.ref = "commonmark" }
commonmark-ext-gfm-strikethrough = { group = "org.commonmark", name = "commonmark-ext-gfm-strikethrough", version.ref = "commonmark" }
commonmark-ext-autolink = { group = "org.commonmark", name = "commonmark-ext-autolink", version.ref = "commonmark" }
commonmark-ext-footnotes = { group = "org.commonmark", name = "commonmark-ext-footnotes", version.ref = "commonmark" }
commonmark-ext-ins = { group = "org.commonmark", name = "commonmark-ext-ins", version.ref = "commonmark" }
commonmark-ext-gfm-tables = { group = "org.commonmark", name = "commonmark-ext-gfm-tables", version.ref = "commonmark" }
commonmark-ext-image-attributes = { group = "org.commonmark", name = "commonmark-ext-image-attributes", version.ref = "commonmark" }
commonmark-ext-task-list-items = { group = "org.commonmark", name = "commonmark-ext-task-list-items", version.ref = "commonmark" }
commonmark-ext-heading-anchor = { group = "org.commonmark", name = "commonmark-ext-heading-anchor", version.ref = "commonmark" }

# Firebase
firebase-bom = { group = "com.google.firebase", name = "firebase-bom", version.ref = "firebaseBomVersion" }
google-services-auth = { group = "com.google.android.gms", name = "play-services-auth", version.ref = "googleServicesAuth" }
google-googleid = { group = "com.google.android.libraries.identity.googleid", name = "googleid", version.ref = "googleIdVersion" }
firebase-auth = { module = "com.google.firebase:firebase-auth" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-firestore = { module = "com.google.firebase:firebase-firestore" }
firebase-storage = { module = "com.google.firebase:firebase-storage" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-appcheck-playintegrity = { module = "com.google.firebase:firebase-appcheck-playintegrity" }
firebase-config = { module = "com.google.firebase:firebase-config" }

androidx-window = { module = "androidx.window:window", version.ref = "windowVersion" }
androidx-window-core-android = { group = "androidx.window", name = "window-core-android", version.ref = "windowVersion" }


# DI: Dagger Hilt Android
dagger-hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "diHilt" }
dagger-hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "diHilt" }
dagger-hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "diHiltNaigationCompose" }
firebase-storage-ktx = { group = "com.google.firebase", name = "firebase-storage-ktx", version.ref = "firebaseStorageKtx" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library" }
jetbrains-kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
kotlin-parcelize = { id = "org.jetbrains.kotlin.plugin.parcelize", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "googleServices" }
firebase-crashlytics = { id = "com.google.firebase.crashlytics", version.ref = "firebaseCrashlyticsGradle" }
firebase-perf = { id = "com.google.firebase.firebase-perf", version.ref = "perfPlugin" }
firebase-appdistribution = { id = "com.google.firebase.appdistribution", version.ref = "appdistribution" }
androidx-navigation-safeargs = { id = "androidx.navigation.safeargs", version.ref = "androidxNavigationSafeargs" }
dagger-hilt-plugin = { id = "com.google.dagger.hilt.android", version.ref = "diHilt" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
jetbrains-kotlin-serialization = { id ="org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin"}

