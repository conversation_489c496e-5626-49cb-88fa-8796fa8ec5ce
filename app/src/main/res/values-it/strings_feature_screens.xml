<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="generate_outline" translate_by="gpt">Genera Schema</string>
    <string name="placeholder_default" translate_by="gemini">Inserisci il tuo testo qui…</string>
    <string name="essay_screen_description" translate_by="gemini">Certo! Posso aiutarti. Per favore, fornisci alcuni dettagli per il tuo saggio qui sotto.</string>
    <string name="label_choose_topic" translate_by="gpt">Scegli un argomento</string>
    <string name="label_essay_type" translate_by="gpt">Tipo di saggio</string>
    <string name="label_word_count" translate_by="gpt">Conteggio parole</string>
    <string name="label_language_tone" translate_by="gpt">Lingua + tono</string>
    <string name="placeholder_topic" translate_by="gpt">Descrivi un luogo che ti fa sentire in pace</string>
    <string name="placeholder_essay_type" translate_by="gpt">Esempio: Argomentativo, Narrativo…</string>
    <string name="placeholder_word_count" translate_by="gemini">Es: 300 parole, 500 parole, 1000 parole…</string>
    <string name="placeholder_language_tone" translate_by="gemini">Es: Formale, accademico, …</string>
    <string name="research_screen_description" translate_by="gemini">Un luogo dove trasformare dati grezzi in storie visive significative attraverso ricerca e analisi.</string>
    <string name="label_research_topic" translate_by="gpt">Argomento di ricerca</string>
    <string name="label_research_goal" translate_by="gpt">Obiettivo della ricerca</string>
    <string name="label_preferred_sources" translate_by="gpt">Fonti preferite</string>
    <string name="label_depth_length" translate_by="gpt">Profondità / Lunghezza</string>
    <string name="label_academic_level" translate_by="gpt">Livello accademico</string>
    <string name="placeholder_research_topic" translate_by="gpt">Esempio: Cambiamento climatico, L\'impatto dell\'IA sui lavori, …</string>
    <string name="placeholder_research_goal" translate_by="google">Es: raccolta di informazioni, analisi delle tendenze …</string>
    <string name="placeholder_preferred_sources" translate_by="google">Es: riviste scientifiche, libri, articoli ufficiali</string>
    <string name="placeholder_depth_length" translate_by="gemini">Es: 300 parole, 500 parole, 1000 parole…</string>
    <string name="placeholder_academic_level" translate_by="google">Es: studenti delle scuole superiori, studenti universitari, ricerca avanzata, …</string>
    <string name="literature_screen_description" translate_by="gpt">Dalle parole ai significati nascosti, ti aiutiamo a scoprire il vero valore di ogni opera letteraria.</string>
    <string name="label_title_of_work" translate_by="gpt">Titolo dell\'opera</string>
    <string name="label_author" translate_by="gpt">Autore</string>
    <string name="label_analysis_type" translate_by="gpt">Cosa vuoi analizzare?</string>
    <string name="label_format" translate_by="gpt">Lunghezza / formato</string>
    <string name="placeholder_title" translate_by="gpt">Esempio: Il grande Gatsby</string>
    <string name="placeholder_author" translate_by="google">Es: F. Scott Fitzgerald</string>
    <string name="placeholder_analysis_type" translate_by="gpt">Analisi dei personaggi, Temi principali…</string>
    <string name="placeholder_format" translate_by="gemini">Es: 300 parole, 500 parole, 1000 parole…</string>
    <string name="placeholder_academic_level_literature" translate_by="google">Es: scuola media, liceo o università, …</string>
    <string name="research_outline_topic_label" translate_by="gpt">📘 Argomento di ricerca: %1$s</string>
    <string name="research_outline_goal_label" translate_by="gpt">🎯 Obiettivo di ricerca: %1$s</string>
    <string name="research_outline_sources_label" translate_by="gpt">📚 Fonti Preferite: %1$s</string>
    <string name="research_outline_depth_label" translate_by="gpt">📏 Profondità/Lunghezza: %1$s</string>
    <string name="research_outline_academic_level_label" translate_by="gpt">🎓 Livello Accademico: %1$s</string>
    <string name="research_outline_suggested_outline_label" translate_by="gpt">🧾 Schema suggerito:</string>
    <string name="research_outline_introduction_title" translate_by="gpt">1. Introduzione</string>
    <string name="research_outline_introduction_overview" translate_by="gpt">- Breve panoramica di %1$s</string>
    <string name="research_outline_introduction_importance" translate_by="gemini">Importanza della ricerca a livello %1$s</string>
    <string name="research_outline_objectives_title" translate_by="gpt">2. Obiettivi</string>
    <string name="research_outline_objectives_goal" translate_by="gemini">Chiarisci l\'obiettivo principale: %1$s</string>
    <string name="research_outline_methodology_title" translate_by="gpt">3. Metodologia</string>
    <string name="research_outline_methodology_approach" translate_by="google">- Approccio di ricerca</string>
    <string name="research_outline_methodology_sources" translate_by="gemini">Origini dati: %1$s</string>
    <string name="research_outline_key_insights_title" translate_by="gemini">4. Approfondimenti chiave</string>
    <string name="research_outline_key_insights_trends" translate_by="gpt">- Discutere tendenze, fatti o risultati di analisi</string>
    <string name="research_outline_key_insights_citations" translate_by="gpt">- Usa citazioni se necessario</string>
    <string name="research_outline_conclusion_title" translate_by="gpt">5. Conclusione</string>
    <string name="research_outline_conclusion_summary" translate_by="gpt">- Riepilogo dei risultati</string>
    <string name="research_outline_conclusion_implications" translate_by="gemini">Implicazioni o sviluppi futuri</string>
    <string name="essay_outline_topic_label" translate_by="gemini">✏️ Tema del saggio: %1$s</string>
    <string name="essay_outline_type_label" translate_by="gpt">📝 Tipo di saggio: %1$s</string>
    <string name="essay_outline_word_count_label" translate_by="gpt">🔢 Conteggio parole: %1$s</string>
    <string name="essay_outline_language_tone_label" translate_by="gpt">🗣️ Lingua e tono: %1$s</string>
    <string name="essay_outline_suggested_outline_label" translate_by="gpt">🧾 Schema suggerito:</string>
    <string name="essay_outline_introduction_title" translate_by="gpt">1. Introduzione</string>
    <string name="essay_outline_introduction_topic" translate_by="gemini">Introduci l\'argomento: %1$s</string>
    <string name="essay_outline_introduction_background" translate_by="gpt">- Fornire contesto/sfondo</string>
    <string name="essay_outline_introduction_thesis" translate_by="gemini">Enuncia la tesi</string>
    <string name="essay_outline_body_title" translate_by="gpt">2. Paragrafi del corpo</string>
    <string name="essay_outline_body_paragraph1" translate_by="gpt">- Paragrafo 1: Primo argomento o punto</string>
    <string name="essay_outline_body_paragraph2" translate_by="gemini">Paragrafo 2: Evidenze a supporto o narrativa</string>
    <string name="essay_outline_body_paragraph3" translate_by="gemini">Paragrafo 3: Controargomentazione o dettaglio aggiuntivo</string>
    <string name="essay_outline_conclusion_title" translate_by="gpt">3. Conclusione</string>
    <string name="essay_outline_conclusion_summary" translate_by="gpt">- Riassumi i punti chiave</string>
    <string name="essay_outline_conclusion_restate" translate_by="gpt">- Riformula la tesi in un modo nuovo</string>
    <string name="essay_outline_conclusion_final" translate_by="gpt">- Concludi con un forte pensiero finale</string>
    <string name="essay_outline_notes_title" translate_by="gpt">✨ Note:</string>
    <string name="essay_outline_notes_tone" translate_by="gemini">Mantieni un tono %1$s uniforme.</string>
    <string name="essay_outline_notes_wordcount" translate_by="gemini">Punta a circa %1$s in totale</string>
    <string name="essay_outline_notes_structure" translate_by="gemini">Segui la tipica struttura del saggio %1$s</string>
    <string name="literature_outline_title_label" translate_by="gpt">Titolo: %1$s</string>
    <string name="literature_outline_author_label" translate_by="gpt">Autore: %1$s</string>
    <string name="literature_outline_focus_label" translate_by="gemini">Messa a fuoco: %1$s</string>
    <string name="literature_outline_length_label" translate_by="gpt">Lunghezza: %1$s</string>
    <string name="literature_outline_academic_level_label" translate_by="gpt">Livello Accademico: %1$s</string>
    <string name="literature_outline_outline_label" translate_by="gpt">Schema:</string>
    <string name="literature_outline_introduction_title" translate_by="gpt">1. Introduzione</string>
    <string name="literature_outline_introduction_context" translate_by="gpt">Introduci l\'opera letteraria e il suo contesto.</string>
    <string name="literature_outline_introduction_author" translate_by="gemini">Menziona l\'autore e la rilevanza rispetto al focus di analisi scelto.</string>
    <string name="literature_outline_background_title" translate_by="gpt">2. Sfondo</string>
    <string name="literature_outline_background_summary" translate_by="gpt">Riepilogo della trama o dei personaggi chiave (a seconda del tipo di analisi).</string>
    <string name="literature_outline_background_context" translate_by="gpt">Fornire il contesto necessario per un\'analisi più approfondita.</string>
    <string name="literature_outline_analysis_title" translate_by="gpt">3. Analisi Principale</string>
    <string name="literature_outline_analysis_deep_dive" translate_by="gpt">Approfondisci: %1$s.</string>
    <string name="literature_outline_analysis_evidence" translate_by="gpt">Usa prove dal testo: citazioni, eventi, simbolismo, ecc.</string>
    <string name="literature_outline_connections_title" translate_by="gpt">4. Connessioni</string>
    <string name="literature_outline_connections_themes" translate_by="gpt">Analisi dei collegamenti a temi più ampi o implicazioni nel mondo reale.</string>
    <string name="literature_outline_connections_contrast" translate_by="gemini">Facoltativamente, confronta con altri personaggi o opere.</string>
    <string name="literature_outline_conclusion_title" translate_by="gpt">5. Conclusione</string>
    <string name="literature_outline_conclusion_insights" translate_by="gpt">Riformula i punti chiave.</string>
    <string name="literature_outline_conclusion_value" translate_by="gpt">Rifletti sul valore del lavoro da una prospettiva accademica.</string>
</resources>