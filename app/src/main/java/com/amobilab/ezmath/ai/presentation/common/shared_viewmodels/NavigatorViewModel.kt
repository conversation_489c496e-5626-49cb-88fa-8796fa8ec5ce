package com.amobilab.ezmath.ai.presentation.common.shared_viewmodels

import amobi.module.compose.extentions.PreviewAssist
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import com.amobilab.ezmath.ai.presentation.navigation.AppNavigator
import com.amobilab.ezmath.ai.presentation.navigation.MainComposeActivity
import com.amobilab.ezmath.ai.presentation.navigation.ScreenRoutes
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class NavigatorViewModel @Inject constructor(
    private val appNavigator: AppNavigator
) : ViewModel() {
    companion object {
        const val TAG = "NavigatorViewModel"

        @Composable
        fun getInstance(): NavigatorViewModel {
            return hiltViewModel<NavigatorViewModel>(
                viewModelStoreOwner =
                    if (PreviewAssist.IS_PREVIEW)
                        checkNotNull(LocalViewModelStoreOwner.current)
                    else
                        LocalContext.current as MainComposeActivity,
            )
        }
    }
    
    fun navigateTo(appNavScreen: ScreenRoutes) {
        appNavigator.navigateTo(appNavScreen)
    }

    fun navigateBack() {
        appNavigator.navigateBack()
    }
}