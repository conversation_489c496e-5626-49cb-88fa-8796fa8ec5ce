package amobi.module.common.views

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable

abstract class CommDialog(
    context: Context,
    private val onClickPositive: (() -> Unit?)? = null,
    private val onClickNegative: (() -> Unit?)? = null,
    private val onDismissEvent: (() -> Unit?)? = null,
) : Dialog(context) {
    fun setupDialogWindow(
        dimAmount: Float? = null,
        gravity: Int? = null,
    ) {
        this.window?.let {
            it.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
            it.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            it.setGravity(Gravity.CENTER)
            // margin horizontal 16dp
            val margin = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, 16f, context.resources.displayMetrics)
            val params = it.attributes
            params.width = ViewGroup.LayoutParams.MATCH_PARENT
            params.height = ViewGroup.LayoutParams.WRAP_CONTENT
            params.horizontalMargin = margin
            it.attributes = params

            if (dimAmount != null)
                it.setDimAmount(dimAmount)

            if (gravity != null)
                it.setGravity(gravity)
        }
    }
}
